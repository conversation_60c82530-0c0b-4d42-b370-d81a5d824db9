{"name": "hello-world-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo-google-fonts/manrope": "^0.4.1", "@expo/config-plugins": "~10.0.0", "@expo/ngrok": "^4.1.3", "@expo/prebuild-config": "~9.0.0", "expo": "~53.0.10", "expo-font": "^13.3.1", "expo-linear-gradient": "^14.1.5", "expo-router": "~5.0.7", "expo-splash-screen": "^0.30.9", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.3", "react-native-safe-area-context": "5.4.0", "react-native-screens": "^4.11.1", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.25.2", "http-server": "^14.1.1", "typescript": "~5.8.3"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false}}}, "private": true}